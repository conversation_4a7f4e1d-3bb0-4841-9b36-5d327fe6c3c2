import React, { useEffect } from 'react';
import { useHistory } from '@docusaurus/router';
import Layout from '@theme/Layout';

export default function NotFound() {
  const history = useHistory();

  useEffect(() => {
    // Redirect to docs after a short delay
    const timer = setTimeout(() => {
      history.push('/docs/revision-history');
    }, 2000);

    return () => clearTimeout(timer);
  }, [history]);

  return (
    <Layout title="Page Not Found">
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '50vh',
          fontSize: '20px',
          flexDirection: 'column',
        }}
      >
        <h1>Page Not Found</h1>
        <p>Redirecting you to the documentation...</p>
        <p>
          If you are not redirected automatically,{' '}
          <a href="/docs/revision-history">click here</a>.
        </p>
      </div>
    </Layout>
  );
}
